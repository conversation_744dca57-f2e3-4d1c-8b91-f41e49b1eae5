# 快速开始指南

## 1. 安装

### Windows用户
双击运行 `install.bat` 文件，脚本会自动完成所有安装步骤。

### 手动安装
```bash
# 1. 安装Python依赖
pip install -r requirements.txt

# 2. 安装Playwright浏览器
playwright install chromium

# 3. 运行安装脚本
python setup.py
```

## 2. 测试安装

### Windows用户
双击运行 `run_test.bat` 文件

### 命令行
```bash
python test_crawler.py
```

## 3. 使用方法

### 方法一：GUI界面（推荐新手）

#### Windows用户
双击运行 `run_gui.bat` 文件

#### 命令行
```bash
python gui.py
```

### 方法二：命令行

#### 搜索单个零件
```bash
python main.py --mode search --part-number "ATMEGA328P-AU"
```

#### 分析BOM文件
```bash
python main.py --mode analyze --bom-file example_bom.csv --output results.xlsx
```

## 4. BOM文件格式

创建一个CSV文件，包含以下列：

| reference | part_number | description | quantity | manufacturer |
|-----------|-------------|-------------|----------|--------------|
| R1 | RC0603FR-0710KL | RES SMD 10K OHM | 2 | Yageo |
| C1 | CL10B104KB8NNNC | CAP CER 0.1UF | 3 | Samsung |
| U1 | ATMEGA328P-AU | MCU 8BIT 32KB | 1 | Microchip |

**必需列：**
- `reference`: 参考标识符（如R1, C2, U1）
- `part_number`: 制造商零件编号
- `quantity`: 数量

**可选列：**
- `description`: 零件描述
- `manufacturer`: 制造商

## 5. 输出结果

分析完成后会生成包含以下信息的结果文件：

- 原始BOM信息
- 搜索状态（找到/未找到/错误）
- 匹配的产品信息
- 价格和库存信息
- 总成本估算

## 6. 常见问题

### Q: 浏览器启动失败
**A:** 运行 `playwright install chromium` 重新安装浏览器

### Q: 搜索结果为空
**A:** 
- 检查零件编号是否正确
- 确认网络连接正常
- 查看日志文件了解详细错误

### Q: 程序运行缓慢
**A:**
- 减少并发请求数（修改config.py中的设置）
- 检查网络连接速度
- 分批处理大型BOM文件

### Q: 内存不足
**A:**
- 关闭其他程序释放内存
- 分批处理BOM文件
- 减少并发数量

## 7. 高级用法

### 自定义列名
如果你的BOM文件使用不同的列名：

```bash
python main.py --mode analyze \
    --bom-file my_bom.csv \
    --part-column "零件编号" \
    --qty-column "数量" \
    --ref-column "位号"
```

### 指定输出格式
```bash
python main.py --mode analyze \
    --bom-file example_bom.csv \
    --output results.xlsx \
    --output-format xlsx
```

### 调整日志级别
```bash
python main.py --mode analyze \
    --bom-file example_bom.csv \
    --log-level DEBUG
```

## 8. 获取帮助

查看完整的命令行选项：
```bash
python main.py --help
```

## 9. 项目结构

```
bom_crawler/
├── main.py              # 主程序
├── gui.py               # GUI界面
├── test_crawler.py      # 测试脚本
├── example_bom.csv      # 示例BOM文件
├── install.bat          # Windows安装脚本
├── run_gui.bat          # Windows GUI启动脚本
├── run_test.bat         # Windows测试脚本
└── README.md            # 详细文档
```

## 10. 下一步

- 阅读 `README.md` 了解更多功能
- 查看 `config.py` 自定义配置
- 修改 `example_bom.csv` 测试你的BOM文件
