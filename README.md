# BOM爬虫工具

一个用于从电子元器件分销商网站爬取BOM（物料清单）信息的Python工具。目前支持DigiKey网站。

## 功能特性

- 🔍 **智能搜索**: 支持零件编号精确搜索和模糊匹配
- 📊 **批量分析**: 支持批量分析整个BOM文件
- 💰 **价格获取**: 自动获取价格阶梯和库存信息
- 📈 **成本计算**: 自动计算BOM总成本
- 📄 **多格式支持**: 支持CSV、Excel、JSON等多种输出格式
- 🚀 **异步处理**: 使用异步爬虫提高处理效率
- 🛡️ **反爬保护**: 内置随机延迟和User-Agent轮换

## 支持的分销商

- ✅ DigiKey (https://www.digikey.cn)
- 🚧 Mouser (计划中)
- 🚧 Arrow (计划中)

## 安装

1. 克隆项目
```bash
git clone <repository-url>
cd bom_crawler
```

2. 安装依赖
```bash
pip install -r requirements.txt
```

3. 安装Playwright浏览器
```bash
playwright install chromium
```

## 使用方法

### 1. 搜索单个零件

```bash
python main.py --mode search --part-number "ATMEGA328P-AU"
```

### 2. 分析BOM文件

```bash
python main.py --mode analyze --bom-file example_bom.csv --output results.xlsx
```

### 3. 自定义列名

如果你的BOM文件使用不同的列名：

```bash
python main.py --mode analyze \
    --bom-file my_bom.csv \
    --part-column "零件编号" \
    --qty-column "数量" \
    --ref-column "位号" \
    --output results.csv
```

### 4. 指定分销商

```bash
python main.py --mode analyze --bom-file example_bom.csv --distributor digikey
```

## 命令行参数

### 通用参数
- `--mode`: 运行模式 (`search` 或 `analyze`)
- `--distributor`: 分销商选择 (默认: `digikey`)
- `--log-level`: 日志级别 (`DEBUG`, `INFO`, `WARNING`, `ERROR`)

### 搜索模式参数
- `--part-number`: 要搜索的零件编号

### 分析模式参数
- `--bom-file`: BOM文件路径
- `--output`: 输出文件路径 (可选，默认自动生成)
- `--output-format`: 输出格式 (`csv`, `xlsx`, `json`)
- `--part-column`: 零件编号列名 (默认: `part_number`)
- `--qty-column`: 数量列名 (默认: `quantity`)
- `--ref-column`: 参考标识符列名 (默认: `reference`)

## BOM文件格式

支持的BOM文件格式：
- CSV (.csv)
- Excel (.xlsx, .xls)

### 必需列
- **零件编号**: 制造商零件编号
- **数量**: 所需数量
- **参考标识符**: 如R1, C2, U1等

### 可选列
- **描述**: 零件描述
- **制造商**: 制造商名称

### 示例BOM文件

```csv
reference,part_number,description,quantity,manufacturer
R1,RC0603FR-0710KL,RES SMD 10K OHM 1% 1/10W 0603,2,Yageo
C1,CL10B104KB8NNNC,CAP CER 0.1UF 50V X7R 0603,3,Samsung
U1,ATMEGA328P-AU,MCU 8BIT 32KB FLASH 32TQFP,1,Microchip
```

## 输出结果

分析结果包含以下信息：

### 摘要信息
- 总条目数
- 找到产品数量
- 未找到产品数量
- 错误数量
- 总成本估算
- 成功率

### 详细信息
- 原始BOM信息
- 搜索状态
- 匹配的产品列表
- 选中的最佳匹配产品
- 价格阶梯信息
- 库存状态
- 产品规格

## 项目结构

```
bom_crawler/
├── main.py              # 主程序入口
├── config.py            # 配置文件
├── models.py            # 数据模型
├── utils.py             # 工具函数
├── base_crawler.py      # 基础爬虫类
├── digikey_crawler.py   # DigiKey爬虫实现
├── bom_analyzer.py      # BOM分析器
├── example_bom.csv      # 示例BOM文件
├── requirements.txt     # 依赖包列表
├── data/               # 数据目录
│   ├── downloads/      # 下载文件
│   └── processed/      # 处理结果
└── logs/               # 日志文件
```

## 配置说明

主要配置项在 `config.py` 中：

- `CRAWLER_CONFIG`: 爬虫配置（超时、重试次数、延迟等）
- `SUPPORTED_DISTRIBUTORS`: 支持的分销商列表
- `OUTPUT_FORMATS`: 支持的输出格式
- `SEARCH_CONFIG`: 搜索配置（最大结果数、并发数等）

## 注意事项

1. **合规使用**: 请遵守网站的robots.txt和使用条款
2. **频率限制**: 内置了随机延迟，避免过于频繁的请求
3. **网络环境**: 确保网络连接稳定，某些地区可能需要代理
4. **数据准确性**: 爬取的数据仅供参考，实际采购请以官网为准

## 故障排除

### 常见问题

1. **浏览器启动失败**
   ```bash
   playwright install chromium
   ```

2. **网络连接超时**
   - 检查网络连接
   - 尝试增加超时时间
   - 考虑使用代理

3. **零件搜索失败**
   - 检查零件编号格式
   - 尝试不同的搜索关键词
   - 查看日志文件获取详细错误信息

4. **内存不足**
   - 减少并发请求数
   - 分批处理大型BOM文件

## 开发计划

- [ ] 支持更多分销商 (Mouser, Arrow, RS Components)
- [ ] 添加GUI界面
- [ ] 支持更多BOM文件格式
- [ ] 添加价格历史跟踪
- [ ] 支持替代料推荐
- [ ] 添加库存预警功能

## 贡献

欢迎提交Issue和Pull Request！

## 许可证

MIT License
