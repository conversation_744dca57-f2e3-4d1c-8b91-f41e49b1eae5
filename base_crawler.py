"""
基础爬虫类
"""
import asyncio
import time
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from playwright.async_api import async_playwright, <PERSON><PERSON>er, BrowserContext, Page
from loguru import logger

from models import ProductInfo, CrawlerResult
from utils import async_random_delay, get_random_user_agent
from config import CRAWLER_CONFIG


class BaseCrawler(ABC):
    """基础爬虫抽象类"""
    
    def __init__(self, distributor: str):
        self.distributor = distributor
        self.config = CRAWLER_CONFIG.get(distributor, {})
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.start_browser()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close_browser()
    
    async def start_browser(self) -> None:
        """启动浏览器"""
        try:
            playwright = await async_playwright().start()
            
            # 启动浏览器
            self.browser = await playwright.chromium.launch(
                headless=self.config.get("headless", True),
                args=[
                    '--no-sandbox',
                    '--disable-blink-features=AutomationControlled',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor'
                ]
            )
            
            # 创建浏览器上下文
            self.context = await self.browser.new_context(
                viewport=self.config.get("viewport", {"width": 1920, "height": 1080}),
                user_agent=self.config.get("user_agent", get_random_user_agent())
            )
            
            # 创建页面
            self.page = await self.context.new_page()
            
            # 设置超时
            self.page.set_default_timeout(self.config.get("timeout", 30000))
            
            logger.info(f"{self.distributor} 浏览器启动成功")
            
        except Exception as e:
            logger.error(f"启动浏览器失败: {e}")
            raise
    
    async def close_browser(self) -> None:
        """关闭浏览器"""
        try:
            if self.page:
                await self.page.close()
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            
            logger.info(f"{self.distributor} 浏览器已关闭")
            
        except Exception as e:
            logger.error(f"关闭浏览器失败: {e}")
    
    async def navigate_to_url(self, url: str) -> bool:
        """导航到指定URL"""
        try:
            if not self.page:
                raise RuntimeError("浏览器页面未初始化")
            
            await self.page.goto(url, wait_until="networkidle")
            await async_random_delay(1, 2)
            
            logger.info(f"成功导航到: {url}")
            return True
            
        except Exception as e:
            logger.error(f"导航失败 {url}: {e}")
            return False
    
    async def wait_for_element(self, selector: str, timeout: int = 10000) -> bool:
        """等待元素出现"""
        try:
            if not self.page:
                return False
            
            await self.page.wait_for_selector(selector, timeout=timeout)
            return True
            
        except Exception as e:
            logger.warning(f"等待元素超时 {selector}: {e}")
            return False
    
    async def click_element(self, selector: str) -> bool:
        """点击元素"""
        try:
            if not self.page:
                return False
            
            await self.page.click(selector)
            await async_random_delay(0.5, 1.5)
            return True
            
        except Exception as e:
            logger.error(f"点击元素失败 {selector}: {e}")
            return False
    
    async def fill_input(self, selector: str, text: str) -> bool:
        """填充输入框"""
        try:
            if not self.page:
                return False
            
            await self.page.fill(selector, text)
            await async_random_delay(0.5, 1.0)
            return True
            
        except Exception as e:
            logger.error(f"填充输入框失败 {selector}: {e}")
            return False
    
    async def get_text(self, selector: str) -> Optional[str]:
        """获取元素文本"""
        try:
            if not self.page:
                return None
            
            element = await self.page.query_selector(selector)
            if element:
                return await element.text_content()
            return None
            
        except Exception as e:
            logger.error(f"获取文本失败 {selector}: {e}")
            return None
    
    async def get_attribute(self, selector: str, attribute: str) -> Optional[str]:
        """获取元素属性"""
        try:
            if not self.page:
                return None
            
            element = await self.page.query_selector(selector)
            if element:
                return await element.get_attribute(attribute)
            return None
            
        except Exception as e:
            logger.error(f"获取属性失败 {selector}.{attribute}: {e}")
            return None
    
    async def retry_operation(self, operation, max_retries: int = 3, *args, **kwargs):
        """重试操作"""
        for attempt in range(max_retries):
            try:
                result = await operation(*args, **kwargs)
                return result
            except Exception as e:
                if attempt == max_retries - 1:
                    raise e
                logger.warning(f"操作失败，第 {attempt + 1} 次重试: {e}")
                await async_random_delay(2, 5)
    
    @abstractmethod
    async def search_product(self, part_number: str) -> CrawlerResult:
        """搜索产品 - 抽象方法，需要子类实现"""
        pass
    
    @abstractmethod
    async def get_product_details(self, product_url: str) -> CrawlerResult:
        """获取产品详情 - 抽象方法，需要子类实现"""
        pass
