"""
BOM分析器
"""
import asyncio
from typing import List, Dict, Any, Optional
from pathlib import Path
from loguru import logger

from models import BOMItem, BOMAnalysisResult, ProductInfo
from digikey_crawler import DigiKeyCrawler
from utils import load_bom_file, normalize_part_number, validate_part_number
from config import SUPPORTED_DISTRIBUTORS


class BOMAnalyzer:
    """BOM分析器"""
    
    def __init__(self, distributor: str = "digikey"):
        self.distributor = distributor
        self.crawler = None
        
        if distributor not in SUPPORTED_DISTRIBUTORS:
            raise ValueError(f"不支持的分销商: {distributor}")
    
    async def analyze_bom_file(self, bom_file_path: Path, 
                              part_number_column: str = "part_number",
                              quantity_column: str = "quantity",
                              reference_column: str = "reference") -> BOMAnalysisResult:
        """分析BOM文件"""
        try:
            # 加载BOM文件
            bom_data = load_bom_file(bom_file_path)
            logger.info(f"加载BOM文件: {len(bom_data)} 条记录")
            
            # 转换为BOMItem列表
            bom_items = self._convert_to_bom_items(
                bom_data, part_number_column, quantity_column, reference_column
            )
            
            # 分析每个BOM项目
            analyzed_items = await self._analyze_bom_items(bom_items)
            
            # 生成分析结果
            result = self._generate_analysis_result(analyzed_items)
            
            logger.info(f"BOM分析完成: {result.found_items}/{result.total_items} 找到")
            return result
            
        except Exception as e:
            logger.error(f"分析BOM文件失败: {e}")
            raise
    
    def _convert_to_bom_items(self, bom_data: List[Dict[str, Any]], 
                             part_number_column: str,
                             quantity_column: str,
                             reference_column: str) -> List[BOMItem]:
        """转换为BOMItem列表"""
        bom_items = []
        
        for i, row in enumerate(bom_data):
            try:
                # 获取零件编号
                part_number = str(row.get(part_number_column, "")).strip()
                if not part_number:
                    logger.warning(f"第 {i+1} 行缺少零件编号")
                    continue
                
                # 获取数量
                quantity_str = str(row.get(quantity_column, "1")).strip()
                try:
                    quantity = int(float(quantity_str))
                except (ValueError, TypeError):
                    quantity = 1
                    logger.warning(f"第 {i+1} 行数量格式错误，使用默认值1")
                
                # 获取参考标识符
                reference = str(row.get(reference_column, f"U{i+1}")).strip()
                
                # 获取描述
                description = str(row.get("description", "")).strip() or None
                
                # 获取制造商
                manufacturer = str(row.get("manufacturer", "")).strip() or None
                
                bom_item = BOMItem(
                    reference=reference,
                    part_number=normalize_part_number(part_number),
                    description=description,
                    quantity=quantity,
                    manufacturer=manufacturer
                )
                
                bom_items.append(bom_item)
                
            except Exception as e:
                logger.error(f"处理第 {i+1} 行失败: {e}")
                continue
        
        return bom_items
    
    async def _analyze_bom_items(self, bom_items: List[BOMItem]) -> List[BOMItem]:
        """分析BOM项目"""
        if self.distributor == "digikey":
            self.crawler = DigiKeyCrawler()
        else:
            raise ValueError(f"不支持的分销商: {self.distributor}")
        
        analyzed_items = []
        
        async with self.crawler:
            # 使用信号量限制并发数
            semaphore = asyncio.Semaphore(3)  # 最多3个并发请求
            
            tasks = []
            for item in bom_items:
                task = self._analyze_single_item(semaphore, item)
                tasks.append(task)
            
            # 执行所有任务
            analyzed_items = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理异常结果
            final_items = []
            for i, result in enumerate(analyzed_items):
                if isinstance(result, Exception):
                    logger.error(f"分析项目失败 {bom_items[i].part_number}: {result}")
                    bom_items[i].search_status = "error"
                    bom_items[i].error_message = str(result)
                    final_items.append(bom_items[i])
                else:
                    final_items.append(result)
            
            return final_items
    
    async def _analyze_single_item(self, semaphore: asyncio.Semaphore, 
                                  bom_item: BOMItem) -> BOMItem:
        """分析单个BOM项目"""
        async with semaphore:
            try:
                # 验证零件编号
                if not validate_part_number(bom_item.part_number):
                    bom_item.search_status = "error"
                    bom_item.error_message = "无效的零件编号"
                    return bom_item
                
                logger.info(f"搜索零件: {bom_item.part_number}")
                
                # 搜索产品
                search_result = await self.crawler.search_product(bom_item.part_number)
                
                if search_result.success and search_result.data:
                    bom_item.search_results = search_result.data
                    bom_item.search_status = "found"
                    
                    # 选择最佳匹配的产品
                    best_match = self._select_best_match(bom_item)
                    if best_match:
                        bom_item.selected_product = best_match
                    
                    logger.info(f"找到 {len(search_result.data)} 个匹配产品: {bom_item.part_number}")
                    
                elif search_result.success:
                    bom_item.search_status = "not_found"
                    logger.warning(f"未找到产品: {bom_item.part_number}")
                    
                else:
                    bom_item.search_status = "error"
                    bom_item.error_message = search_result.error_message
                    logger.error(f"搜索失败 {bom_item.part_number}: {search_result.error_message}")
                
                # 添加延迟避免过于频繁的请求
                await asyncio.sleep(1)
                
                return bom_item
                
            except Exception as e:
                logger.error(f"分析项目失败 {bom_item.part_number}: {e}")
                bom_item.search_status = "error"
                bom_item.error_message = str(e)
                return bom_item
    
    def _select_best_match(self, bom_item: BOMItem) -> Optional[ProductInfo]:
        """选择最佳匹配的产品"""
        if not bom_item.search_results:
            return None
        
        # 优先选择完全匹配的零件编号
        for product in bom_item.search_results:
            if product.part_number.upper() == bom_item.part_number.upper():
                return product
        
        # 如果没有完全匹配，选择第一个结果
        return bom_item.search_results[0]
    
    def _generate_analysis_result(self, bom_items: List[BOMItem]) -> BOMAnalysisResult:
        """生成分析结果"""
        total_items = len(bom_items)
        found_items = sum(1 for item in bom_items if item.search_status == "found")
        not_found_items = sum(1 for item in bom_items if item.search_status == "not_found")
        error_items = sum(1 for item in bom_items if item.search_status == "error")
        
        # 计算总成本
        total_cost = 0.0
        for item in bom_items:
            if item.selected_product and item.selected_product.price_breaks:
                # 使用第一个价格阶梯计算成本
                unit_price = item.selected_product.price_breaks[0].unit_price
                total_cost += unit_price * item.quantity
        
        return BOMAnalysisResult(
            total_items=total_items,
            found_items=found_items,
            not_found_items=not_found_items,
            error_items=error_items,
            total_cost=total_cost,
            currency="USD",
            items=bom_items,
            distributor=self.distributor
        )
