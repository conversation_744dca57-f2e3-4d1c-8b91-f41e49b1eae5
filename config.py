"""
配置文件
"""
import os
from pathlib import Path
from typing import Dict, List

# 项目根目录
PROJECT_ROOT = Path(__file__).parent

# 数据目录
DATA_DIR = PROJECT_ROOT / "data"
DOWNLOADS_DIR = DATA_DIR / "downloads"
PROCESSED_DIR = DATA_DIR / "processed"
LOGS_DIR = PROJECT_ROOT / "logs"

# 创建必要的目录
for dir_path in [DATA_DIR, DOWNLOADS_DIR, PROCESSED_DIR, LOGS_DIR]:
    dir_path.mkdir(exist_ok=True)

# 爬虫配置
CRAWLER_CONFIG = {
    "digikey": {
        "base_url": "https://www.digikey.cn",
        "search_url": "https://www.digikey.cn/zh/products/filter",
        "timeout": 30000,  # 30秒
        "retry_times": 3,
        "delay_range": (1, 3),  # 随机延迟1-3秒
        "headless": True,
        "viewport": {"width": 1920, "height": 1080},
        "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like G<PERSON>o) Chrome/120.0.0.0 Safari/537.36"
    }
}

# 支持的电子元器件分销商
SUPPORTED_DISTRIBUTORS = {
    "digikey": {
        "name": "DigiKey",
        "url": "https://www.digikey.cn",
        "crawler_class": "DigiKeyCrawler"
    },
    # 可以扩展其他分销商
    # "mouser": {
    #     "name": "Mouser",
    #     "url": "https://www.mouser.cn",
    #     "crawler_class": "MouserCrawler"
    # }
}

# 日志配置
LOG_CONFIG = {
    "level": "INFO",
    "format": "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}",
    "rotation": "10 MB",
    "retention": "7 days"
}

# 输出格式配置
OUTPUT_FORMATS = ["csv", "xlsx", "json"]
DEFAULT_OUTPUT_FORMAT = "csv"

# BOM文件支持的格式
SUPPORTED_BOM_FORMATS = [".csv", ".xlsx", ".xls", ".txt"]

# 产品搜索配置
SEARCH_CONFIG = {
    "max_results_per_query": 500,  # 每次搜索最大结果数
    "batch_size": 25,  # 批处理大小
    "concurrent_requests": 3,  # 并发请求数
}
