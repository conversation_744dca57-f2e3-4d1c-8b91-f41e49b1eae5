"""
DigiKey爬虫实现
"""
import re
import csv
import asyncio
from typing import List, Dict, Any, Optional
from pathlib import Path
from loguru import logger

from base_crawler import BaseCrawler
from models import ProductInfo, PriceBreak, CrawlerResult
from utils import parse_price, parse_quantity, clean_text, normalize_part_number
from config import DOWNLOADS_DIR


class DigiKeyCrawler(BaseCrawler):
    """DigiKey爬虫"""
    
    def __init__(self):
        super().__init__("digikey")
        self.base_url = "https://www.digikey.cn"
        self.search_url = f"{self.base_url}/zh/products/filter"
    
    async def search_product(self, part_number: str) -> CrawlerResult:
        """搜索产品"""
        start_time = asyncio.get_event_loop().time()
        
        try:
            # 标准化零件编号
            clean_part_number = normalize_part_number(part_number)
            
            if not clean_part_number:
                return CrawlerResult(
                    success=False,
                    error_message="无效的零件编号",
                    execution_time=asyncio.get_event_loop().time() - start_time
                )
            
            # 构建搜索URL
            search_url = f"{self.base_url}/zh/products/result?s={clean_part_number}"
            
            # 导航到搜索页面
            if not await self.navigate_to_url(search_url):
                return CrawlerResult(
                    success=False,
                    error_message="无法访问搜索页面",
                    execution_time=asyncio.get_event_loop().time() - start_time
                )
            
            # 等待搜索结果加载
            await asyncio.sleep(3)
            
            # 检查是否有搜索结果
            results_selector = "table[data-testid='data-table']"
            if not await self.wait_for_element(results_selector, timeout=10000):
                return CrawlerResult(
                    success=True,
                    data=[],
                    execution_time=asyncio.get_event_loop().time() - start_time
                )
            
            # 解析搜索结果
            products = await self._parse_search_results()
            
            return CrawlerResult(
                success=True,
                data=products,
                execution_time=asyncio.get_event_loop().time() - start_time
            )
            
        except Exception as e:
            logger.error(f"搜索产品失败 {part_number}: {e}")
            return CrawlerResult(
                success=False,
                error_message=str(e),
                execution_time=asyncio.get_event_loop().time() - start_time
            )
    
    async def _parse_search_results(self) -> List[ProductInfo]:
        """解析搜索结果"""
        products = []
        
        try:
            # 获取表格行
            rows = await self.page.query_selector_all("table[data-testid='data-table'] tbody tr")
            
            for row in rows[:10]:  # 限制前10个结果
                try:
                    product = await self._parse_product_row(row)
                    if product:
                        products.append(product)
                except Exception as e:
                    logger.warning(f"解析产品行失败: {e}")
                    continue
            
            logger.info(f"解析到 {len(products)} 个产品")
            
        except Exception as e:
            logger.error(f"解析搜索结果失败: {e}")
        
        return products
    
    async def _parse_product_row(self, row) -> Optional[ProductInfo]:
        """解析产品行"""
        try:
            # 零件编号和制造商
            part_cell = await row.query_selector("td:nth-child(2)")
            if not part_cell:
                return None
            
            # 获取零件编号
            part_link = await part_cell.query_selector("a")
            if not part_link:
                return None
            
            part_number = clean_text(await part_link.text_content() or "")
            product_url = await part_link.get_attribute("href")
            if product_url and not product_url.startswith("http"):
                product_url = f"{self.base_url}{product_url}"
            
            # 获取描述
            description_elem = await part_cell.query_selector("div:last-child")
            description = clean_text(await description_elem.text_content() or "") if description_elem else ""
            
            # 获取制造商
            manufacturer_elem = await part_cell.query_selector("a[href*='/supplier-centers/']")
            manufacturer = clean_text(await manufacturer_elem.text_content() or "") if manufacturer_elem else ""
            
            # 获取库存
            stock_cell = await row.query_selector("td:nth-child(3)")
            stock_text = clean_text(await stock_cell.text_content() or "") if stock_cell else "0"
            stock_quantity = parse_quantity(stock_text)
            
            # 获取价格
            price_cell = await row.query_selector("td:nth-child(4)")
            price_breaks = []
            if price_cell:
                price_text = clean_text(await price_cell.text_content() or "")
                price_breaks = self._parse_price_breaks(price_text)
            
            # 获取系列
            series_cell = await row.query_selector("td:nth-child(5)")
            series = clean_text(await series_cell.text_content() or "") if series_cell else None
            
            # 获取包装
            package_cell = await row.query_selector("td:nth-child(6)")
            package_type = clean_text(await package_cell.text_content() or "") if package_cell else None
            
            # 获取产品状态
            status_cell = await row.query_selector("td:nth-child(7)")
            product_status = clean_text(await status_cell.text_content() or "") if status_cell else None
            
            return ProductInfo(
                part_number=part_number,
                manufacturer=manufacturer,
                description=description,
                stock_quantity=stock_quantity,
                price_breaks=price_breaks,
                series=series,
                package_type=package_type,
                product_status=product_status,
                distributor="DigiKey",
                product_url=product_url
            )
            
        except Exception as e:
            logger.error(f"解析产品行失败: {e}")
            return None
    
    def _parse_price_breaks(self, price_text: str) -> List[PriceBreak]:
        """解析价格阶梯"""
        price_breaks = []
        
        if not price_text:
            return price_breaks
        
        # 匹配价格格式：1 : $2.45000
        pattern = r'(\d+)\s*:\s*\$?([\d,]+\.?\d*)'
        matches = re.findall(pattern, price_text)
        
        for qty_str, price_str in matches:
            try:
                quantity = int(qty_str)
                price = parse_price(price_str)
                
                if quantity > 0 and price > 0:
                    price_breaks.append(PriceBreak(
                        quantity=quantity,
                        unit_price=price,
                        currency="USD"
                    ))
            except ValueError:
                continue
        
        return price_breaks
    
    async def get_product_details(self, product_url: str) -> CrawlerResult:
        """获取产品详情"""
        start_time = asyncio.get_event_loop().time()
        
        try:
            if not await self.navigate_to_url(product_url):
                return CrawlerResult(
                    success=False,
                    error_message="无法访问产品页面",
                    execution_time=asyncio.get_event_loop().time() - start_time
                )
            
            # 等待页面加载
            await asyncio.sleep(2)
            
            # 解析产品详情
            product_details = await self._parse_product_details()
            
            return CrawlerResult(
                success=True,
                data=product_details,
                execution_time=asyncio.get_event_loop().time() - start_time
            )
            
        except Exception as e:
            logger.error(f"获取产品详情失败 {product_url}: {e}")
            return CrawlerResult(
                success=False,
                error_message=str(e),
                execution_time=asyncio.get_event_loop().time() - start_time
            )
    
    async def _parse_product_details(self) -> Dict[str, Any]:
        """解析产品详情页面"""
        details = {}
        
        try:
            # 获取规格书链接
            datasheet_link = await self.page.query_selector("a[href*='.pdf']")
            if datasheet_link:
                details["datasheet_url"] = await datasheet_link.get_attribute("href")
            
            # 获取产品图片
            image_elem = await self.page.query_selector("img[alt*='产品图片']")
            if image_elem:
                details["image_url"] = await image_elem.get_attribute("src")
            
            # 获取技术规格表
            specs_table = await self.page.query_selector("table.specifications")
            if specs_table:
                specs = {}
                rows = await specs_table.query_selector_all("tr")
                for row in rows:
                    cells = await row.query_selector_all("td")
                    if len(cells) >= 2:
                        key = clean_text(await cells[0].text_content() or "")
                        value = clean_text(await cells[1].text_content() or "")
                        if key and value:
                            specs[key] = value
                details["specifications"] = specs
            
        except Exception as e:
            logger.error(f"解析产品详情失败: {e}")
        
        return details

    async def download_search_results(self, part_number: str, max_results: int = 250) -> CrawlerResult:
        """下载搜索结果为CSV"""
        start_time = asyncio.get_event_loop().time()

        try:
            # 搜索产品
            search_result = await self.search_product(part_number)
            if not search_result.success:
                return search_result

            # 如果有搜索结果，尝试下载CSV
            if search_result.data:
                csv_file = await self._download_csv_file(max_results)
                if csv_file:
                    # 解析CSV文件
                    products = await self._parse_csv_file(csv_file)
                    return CrawlerResult(
                        success=True,
                        data=products,
                        execution_time=asyncio.get_event_loop().time() - start_time
                    )

            return search_result

        except Exception as e:
            logger.error(f"下载搜索结果失败 {part_number}: {e}")
            return CrawlerResult(
                success=False,
                error_message=str(e),
                execution_time=asyncio.get_event_loop().time() - start_time
            )

    async def _download_csv_file(self, max_results: int = 250) -> Optional[Path]:
        """下载CSV文件"""
        try:
            # 查找下载表格按钮
            download_button = "button[data-testid='download-table-popup-trigger-button']"
            if not await self.wait_for_element(download_button, timeout=5000):
                logger.warning("未找到下载表格按钮")
                return None

            # 点击下载按钮
            await self.click_element(download_button)

            # 等待下载对话框出现
            dialog_selector = "dialog[aria-label='下载结果']"
            if not await self.wait_for_element(dialog_selector, timeout=5000):
                logger.warning("下载对话框未出现")
                return None

            # 选择下载选项
            if max_results <= 25:
                # 选择当前可见结果
                option_selector = "input[value='current']"
            elif max_results <= 250:
                # 选择前250条结果
                option_selector = "input[value='250']"
            else:
                # 选择前500条结果
                option_selector = "input[value='500']"

            if await self.page.query_selector(option_selector):
                await self.click_element(option_selector)

            # 设置下载监听
            download_path = DOWNLOADS_DIR / f"digikey_results_{int(asyncio.get_event_loop().time())}.csv"

            # 开始下载
            async with self.page.expect_download() as download_info:
                download_btn = "button[data-testid='download-table-button']"
                await self.click_element(download_btn)

            download = await download_info.value
            await download.save_as(download_path)

            logger.info(f"CSV文件已下载到: {download_path}")
            return download_path

        except Exception as e:
            logger.error(f"下载CSV文件失败: {e}")
            return None

    async def _parse_csv_file(self, csv_file: Path) -> List[ProductInfo]:
        """解析CSV文件"""
        products = []

        try:
            with open(csv_file, 'r', encoding='utf-8-sig') as f:
                reader = csv.DictReader(f)

                for row in reader:
                    try:
                        # 解析价格阶梯
                        price_breaks = []
                        for i in range(1, 6):  # 假设最多5个价格阶梯
                            qty_key = f"数量 {i}" if i > 1 else "数量"
                            price_key = f"单价 {i}" if i > 1 else "单价"

                            if qty_key in row and price_key in row:
                                qty = parse_quantity(row[qty_key])
                                price = parse_price(row[price_key])

                                if qty > 0 and price > 0:
                                    price_breaks.append(PriceBreak(
                                        quantity=qty,
                                        unit_price=price,
                                        currency="USD"
                                    ))

                        product = ProductInfo(
                            part_number=clean_text(row.get("制造商零件编号", "")),
                            manufacturer=clean_text(row.get("制造商", "")),
                            description=clean_text(row.get("描述", "")),
                            stock_quantity=parse_quantity(row.get("现有数量", "0")),
                            price_breaks=price_breaks,
                            series=clean_text(row.get("系列", "")),
                            package_type=clean_text(row.get("包装", "")),
                            product_status=clean_text(row.get("产品状态", "")),
                            distributor="DigiKey",
                            datasheet_url=row.get("规格书链接"),
                            product_url=row.get("产品链接")
                        )

                        products.append(product)

                    except Exception as e:
                        logger.warning(f"解析CSV行失败: {e}")
                        continue

            logger.info(f"从CSV文件解析到 {len(products)} 个产品")

        except Exception as e:
            logger.error(f"解析CSV文件失败: {e}")

        return products
