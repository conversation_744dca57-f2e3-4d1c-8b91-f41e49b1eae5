"""
简单的GUI界面
"""
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import asyncio
import threading
from pathlib import Path
import sys

from bom_analyzer import BOMAnalyzer
from digikey_crawler import DigiKeyCrawler
from utils import setup_logger, save_results
from config import SUPPORTED_DISTRIBUTORS, OUTPUT_FORMATS


class BOMCrawlerGUI:
    """BOM爬虫GUI界面"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("BOM爬虫工具")
        self.root.geometry("800x600")
        
        # 设置日志
        setup_logger()
        
        # 创建界面
        self.create_widgets()
        
        # 当前任务
        self.current_task = None
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="BOM爬虫工具", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 模式选择
        mode_frame = ttk.LabelFrame(main_frame, text="运行模式", padding="10")
        mode_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.mode_var = tk.StringVar(value="analyze")
        ttk.Radiobutton(mode_frame, text="搜索单个零件", variable=self.mode_var, 
                       value="search", command=self.on_mode_change).grid(row=0, column=0, sticky=tk.W)
        ttk.Radiobutton(mode_frame, text="分析BOM文件", variable=self.mode_var, 
                       value="analyze", command=self.on_mode_change).grid(row=0, column=1, sticky=tk.W)
        
        # 搜索模式框架
        self.search_frame = ttk.LabelFrame(main_frame, text="搜索设置", padding="10")
        self.search_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(self.search_frame, text="零件编号:").grid(row=0, column=0, sticky=tk.W)
        self.part_number_var = tk.StringVar()
        ttk.Entry(self.search_frame, textvariable=self.part_number_var, width=30).grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0))
        
        # 分析模式框架
        self.analyze_frame = ttk.LabelFrame(main_frame, text="BOM分析设置", padding="10")
        self.analyze_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # BOM文件选择
        ttk.Label(self.analyze_frame, text="BOM文件:").grid(row=0, column=0, sticky=tk.W)
        self.bom_file_var = tk.StringVar()
        ttk.Entry(self.analyze_frame, textvariable=self.bom_file_var, width=40).grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 5))
        ttk.Button(self.analyze_frame, text="浏览", command=self.browse_bom_file).grid(row=0, column=2)
        
        # 列名设置
        ttk.Label(self.analyze_frame, text="零件编号列:").grid(row=1, column=0, sticky=tk.W, pady=(10, 0))
        self.part_column_var = tk.StringVar(value="part_number")
        ttk.Entry(self.analyze_frame, textvariable=self.part_column_var, width=20).grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=(10, 0))
        
        ttk.Label(self.analyze_frame, text="数量列:").grid(row=2, column=0, sticky=tk.W, pady=(5, 0))
        self.qty_column_var = tk.StringVar(value="quantity")
        ttk.Entry(self.analyze_frame, textvariable=self.qty_column_var, width=20).grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=(5, 0))
        
        ttk.Label(self.analyze_frame, text="参考标识符列:").grid(row=3, column=0, sticky=tk.W, pady=(5, 0))
        self.ref_column_var = tk.StringVar(value="reference")
        ttk.Entry(self.analyze_frame, textvariable=self.ref_column_var, width=20).grid(row=3, column=1, sticky=tk.W, padx=(10, 0), pady=(5, 0))
        
        # 输出设置
        output_frame = ttk.LabelFrame(main_frame, text="输出设置", padding="10")
        output_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(output_frame, text="输出文件:").grid(row=0, column=0, sticky=tk.W)
        self.output_file_var = tk.StringVar()
        ttk.Entry(output_frame, textvariable=self.output_file_var, width=40).grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 5))
        ttk.Button(output_frame, text="浏览", command=self.browse_output_file).grid(row=0, column=2)
        
        ttk.Label(output_frame, text="输出格式:").grid(row=1, column=0, sticky=tk.W, pady=(10, 0))
        self.output_format_var = tk.StringVar(value="csv")
        format_combo = ttk.Combobox(output_frame, textvariable=self.output_format_var, 
                                   values=OUTPUT_FORMATS, state="readonly", width=15)
        format_combo.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=(10, 0))
        
        # 分销商选择
        ttk.Label(output_frame, text="分销商:").grid(row=2, column=0, sticky=tk.W, pady=(10, 0))
        self.distributor_var = tk.StringVar(value="digikey")
        distributor_combo = ttk.Combobox(output_frame, textvariable=self.distributor_var,
                                        values=list(SUPPORTED_DISTRIBUTORS.keys()), 
                                        state="readonly", width=15)
        distributor_combo.grid(row=2, column=1, sticky=tk.W, padx=(10, 0), pady=(10, 0))
        
        # 控制按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=5, column=0, columnspan=3, pady=(10, 0))
        
        self.start_button = ttk.Button(button_frame, text="开始", command=self.start_task)
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_button = ttk.Button(button_frame, text="停止", command=self.stop_task, state="disabled")
        self.stop_button.pack(side=tk.LEFT)
        
        # 进度条
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=6, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # 日志输出
        log_frame = ttk.LabelFrame(main_frame, text="运行日志", padding="10")
        log_frame.grid(row=7, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        main_frame.rowconfigure(7, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, width=80)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
        # 初始化界面状态
        self.on_mode_change()
    
    def on_mode_change(self):
        """模式切换事件"""
        mode = self.mode_var.get()
        
        if mode == "search":
            # 显示搜索框架，隐藏分析框架
            self.search_frame.grid()
            self.analyze_frame.grid_remove()
        else:
            # 显示分析框架，隐藏搜索框架
            self.search_frame.grid_remove()
            self.analyze_frame.grid()
    
    def browse_bom_file(self):
        """浏览BOM文件"""
        filename = filedialog.askopenfilename(
            title="选择BOM文件",
            filetypes=[
                ("CSV文件", "*.csv"),
                ("Excel文件", "*.xlsx *.xls"),
                ("所有文件", "*.*")
            ]
        )
        if filename:
            self.bom_file_var.set(filename)
    
    def browse_output_file(self):
        """浏览输出文件"""
        format_ext = self.output_format_var.get()
        filename = filedialog.asksaveasfilename(
            title="保存结果文件",
            defaultextension=f".{format_ext}",
            filetypes=[
                (f"{format_ext.upper()}文件", f"*.{format_ext}"),
                ("所有文件", "*.*")
            ]
        )
        if filename:
            self.output_file_var.set(filename)
    
    def log_message(self, message):
        """添加日志消息"""
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def start_task(self):
        """开始任务"""
        mode = self.mode_var.get()
        
        if mode == "search":
            part_number = self.part_number_var.get().strip()
            if not part_number:
                messagebox.showerror("错误", "请输入零件编号")
                return
            
            # 在新线程中运行搜索任务
            self.current_task = threading.Thread(target=self.run_search_task, args=(part_number,))
        
        else:  # analyze
            bom_file = self.bom_file_var.get().strip()
            if not bom_file:
                messagebox.showerror("错误", "请选择BOM文件")
                return
            
            if not Path(bom_file).exists():
                messagebox.showerror("错误", "BOM文件不存在")
                return
            
            # 在新线程中运行分析任务
            self.current_task = threading.Thread(target=self.run_analyze_task, args=(bom_file,))
        
        # 更新界面状态
        self.start_button.config(state="disabled")
        self.stop_button.config(state="normal")
        self.progress.start()
        self.log_text.delete(1.0, tk.END)
        
        # 启动任务
        self.current_task.start()
    
    def stop_task(self):
        """停止任务"""
        if self.current_task and self.current_task.is_alive():
            # 注意：这里只是简单的界面状态重置
            # 实际的异步任务停止需要更复杂的实现
            self.log_message("正在停止任务...")
        
        self.reset_ui_state()
    
    def reset_ui_state(self):
        """重置界面状态"""
        self.start_button.config(state="normal")
        self.stop_button.config(state="disabled")
        self.progress.stop()
    
    def run_search_task(self, part_number):
        """运行搜索任务"""
        try:
            self.log_message(f"开始搜索零件: {part_number}")
            
            # 创建新的事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # 运行搜索
            result = loop.run_until_complete(self._search_part(part_number))
            
            if result:
                self.log_message("搜索完成")
            else:
                self.log_message("搜索失败")
        
        except Exception as e:
            self.log_message(f"搜索过程中发生错误: {e}")
        
        finally:
            self.root.after(0, self.reset_ui_state)
    
    def run_analyze_task(self, bom_file):
        """运行分析任务"""
        try:
            self.log_message(f"开始分析BOM文件: {bom_file}")
            
            # 创建新的事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # 运行分析
            result = loop.run_until_complete(self._analyze_bom(bom_file))
            
            if result:
                self.log_message("分析完成")
                messagebox.showinfo("完成", "BOM分析完成！")
            else:
                self.log_message("分析失败")
                messagebox.showerror("错误", "BOM分析失败")
        
        except Exception as e:
            self.log_message(f"分析过程中发生错误: {e}")
            messagebox.showerror("错误", f"分析失败: {e}")
        
        finally:
            self.root.after(0, self.reset_ui_state)
    
    async def _search_part(self, part_number):
        """搜索零件的异步方法"""
        distributor = self.distributor_var.get()
        
        if distributor == "digikey":
            crawler = DigiKeyCrawler()
        else:
            self.log_message(f"不支持的分销商: {distributor}")
            return False
        
        async with crawler:
            result = await crawler.search_product(part_number)
            
            if result.success and result.data:
                self.log_message(f"找到 {len(result.data)} 个匹配产品:")
                
                for i, product in enumerate(result.data[:5], 1):  # 显示前5个结果
                    self.log_message(f"  {i}. {product.part_number} - {product.manufacturer}")
                    self.log_message(f"     描述: {product.description[:60]}...")
                    self.log_message(f"     库存: {product.stock_quantity}")
                    
                    if product.price_breaks:
                        price = product.price_breaks[0]
                        self.log_message(f"     价格: ${price.unit_price} ({price.quantity}+ 件)")
                
                return True
            
            elif result.success:
                self.log_message("未找到匹配的产品")
                return False
            
            else:
                self.log_message(f"搜索失败: {result.error_message}")
                return False
    
    async def _analyze_bom(self, bom_file):
        """分析BOM的异步方法"""
        try:
            analyzer = BOMAnalyzer(self.distributor_var.get())
            
            result = await analyzer.analyze_bom_file(
                Path(bom_file),
                self.part_column_var.get(),
                self.qty_column_var.get(),
                self.ref_column_var.get()
            )
            
            # 显示分析结果
            self.log_message(f"分析结果:")
            self.log_message(f"  总条目数: {result.total_items}")
            self.log_message(f"  找到产品: {result.found_items}")
            self.log_message(f"  未找到: {result.not_found_items}")
            self.log_message(f"  错误: {result.error_items}")
            self.log_message(f"  成功率: {result.found_items/result.total_items*100:.1f}%")
            self.log_message(f"  预估总成本: ${result.total_cost:.2f}")
            
            # 保存结果
            output_file = self.output_file_var.get().strip()
            if output_file:
                # 转换结果为保存格式
                output_data = []
                for item in result.items:
                    row = {
                        "参考标识符": item.reference,
                        "零件编号": item.part_number,
                        "描述": item.description or "",
                        "数量": item.quantity,
                        "搜索状态": item.search_status,
                    }
                    
                    if item.selected_product:
                        row.update({
                            "匹配产品": item.selected_product.part_number,
                            "制造商": item.selected_product.manufacturer,
                            "库存": item.selected_product.stock_quantity,
                        })
                        
                        if item.selected_product.price_breaks:
                            price = item.selected_product.price_breaks[0]
                            row["单价"] = price.unit_price
                            row["总价"] = price.unit_price * item.quantity
                    
                    output_data.append(row)
                
                save_results(output_data, Path(output_file), self.output_format_var.get())
                self.log_message(f"结果已保存到: {output_file}")
            
            return True
            
        except Exception as e:
            self.log_message(f"分析失败: {e}")
            return False


def main():
    """主函数"""
    root = tk.Tk()
    app = BOMCrawlerGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
