@echo off
chcp 65001 >nul
echo ========================================
echo BOM爬虫工具安装脚本
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python
    echo 请先安装Python 3.8或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ 检测到Python环境

:: 升级pip
echo.
echo 升级pip...
python -m pip install --upgrade pip

:: 安装依赖包
echo.
echo 安装依赖包...
python -m pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ 安装依赖包失败
    pause
    exit /b 1
)

:: 安装Playwright
echo.
echo 安装Playwright...
python -m pip install playwright
if errorlevel 1 (
    echo ❌ 安装Playwright失败
    pause
    exit /b 1
)

:: 安装Playwright浏览器
echo.
echo 安装Playwright浏览器...
python -m playwright install chromium
if errorlevel 1 (
    echo ❌ 安装Playwright浏览器失败
    pause
    exit /b 1
)

:: 创建目录
echo.
echo 创建项目目录...
if not exist "data" mkdir data
if not exist "data\downloads" mkdir data\downloads
if not exist "data\processed" mkdir data\processed
if not exist "logs" mkdir logs

echo ✅ 目录创建完成

:: 运行安装测试
echo.
echo 运行安装测试...
python setup.py
if errorlevel 1 (
    echo ❌ 安装测试失败
    pause
    exit /b 1
)

echo.
echo ========================================
echo ✅ 安装完成！
echo ========================================
echo.
echo 下一步操作:
echo 1. 运行测试: python test_crawler.py
echo 2. 搜索零件: python main.py --mode search --part-number "ATMEGA328P-AU"
echo 3. 分析BOM: python main.py --mode analyze --bom-file example_bom.csv
echo 4. 启动GUI: python gui.py
echo.
pause
