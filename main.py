"""
BOM爬虫主程序
"""
import asyncio
import argparse
from pathlib import Path
from loguru import logger

from bom_analyzer import BOMAnalyzer
from digikey_crawler import DigiKeyCrawler
from utils import setup_logger, save_results
from config import SUPPORTED_DISTRIBUTORS, OUTPUT_FORMATS, DEFAULT_OUTPUT_FORMAT


async def search_single_part(part_number: str, distributor: str = "digikey"):
    """搜索单个零件"""
    logger.info(f"搜索零件: {part_number}")
    
    if distributor == "digikey":
        crawler = DigiKeyCrawler()
    else:
        raise ValueError(f"不支持的分销商: {distributor}")
    
    async with crawler:
        result = await crawler.search_product(part_number)
        
        if result.success and result.data:
            logger.info(f"找到 {len(result.data)} 个匹配产品")
            for i, product in enumerate(result.data, 1):
                print(f"\n产品 {i}:")
                print(f"  零件编号: {product.part_number}")
                print(f"  制造商: {product.manufacturer}")
                print(f"  描述: {product.description}")
                print(f"  库存: {product.stock_quantity}")
                if product.price_breaks:
                    print(f"  价格: {product.price_breaks[0].unit_price} {product.price_breaks[0].currency}")
                print(f"  状态: {product.product_status}")
        else:
            logger.warning(f"未找到产品或搜索失败: {result.error_message}")


async def analyze_bom_file(bom_file: Path, output_file: Path = None, 
                          output_format: str = DEFAULT_OUTPUT_FORMAT,
                          distributor: str = "digikey",
                          part_number_column: str = "part_number",
                          quantity_column: str = "quantity",
                          reference_column: str = "reference"):
    """分析BOM文件"""
    logger.info(f"分析BOM文件: {bom_file}")
    
    # 创建分析器
    analyzer = BOMAnalyzer(distributor)
    
    # 分析BOM
    result = await analyzer.analyze_bom_file(
        bom_file, part_number_column, quantity_column, reference_column
    )
    
    # 打印分析结果摘要
    print(f"\n=== BOM分析结果 ===")
    print(f"总条目数: {result.total_items}")
    print(f"找到产品: {result.found_items}")
    print(f"未找到: {result.not_found_items}")
    print(f"错误: {result.error_items}")
    print(f"总成本: ${result.total_cost:.2f} {result.currency}")
    print(f"成功率: {result.found_items/result.total_items*100:.1f}%")
    
    # 保存结果
    if output_file:
        # 转换为字典列表用于保存
        output_data = []
        for item in result.items:
            row = {
                "参考标识符": item.reference,
                "零件编号": item.part_number,
                "描述": item.description or "",
                "数量": item.quantity,
                "制造商": item.manufacturer or "",
                "搜索状态": item.search_status,
                "错误信息": item.error_message or "",
                "找到产品数": len(item.search_results),
            }
            
            # 添加选中产品的信息
            if item.selected_product:
                row.update({
                    "选中产品_零件编号": item.selected_product.part_number,
                    "选中产品_制造商": item.selected_product.manufacturer,
                    "选中产品_描述": item.selected_product.description,
                    "选中产品_库存": item.selected_product.stock_quantity,
                    "选中产品_状态": item.selected_product.product_status,
                    "选中产品_系列": item.selected_product.series,
                    "选中产品_包装": item.selected_product.package_type,
                })
                
                # 添加价格信息
                if item.selected_product.price_breaks:
                    for i, price_break in enumerate(item.selected_product.price_breaks[:3]):
                        row[f"价格阶梯{i+1}_数量"] = price_break.quantity
                        row[f"价格阶梯{i+1}_单价"] = price_break.unit_price
                        row[f"价格阶梯{i+1}_货币"] = price_break.currency
                
                # 计算总价
                if item.selected_product.price_breaks:
                    unit_price = item.selected_product.price_breaks[0].unit_price
                    row["单项总价"] = unit_price * item.quantity
            
            output_data.append(row)
        
        save_results(output_data, output_file, output_format)
        logger.info(f"结果已保存到: {output_file}")
    
    return result


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="BOM爬虫工具")
    parser.add_argument("--mode", choices=["search", "analyze"], required=True,
                       help="运行模式: search(搜索单个零件) 或 analyze(分析BOM文件)")
    
    # 搜索模式参数
    parser.add_argument("--part-number", help="要搜索的零件编号")
    
    # 分析模式参数
    parser.add_argument("--bom-file", type=Path, help="BOM文件路径")
    parser.add_argument("--output", type=Path, help="输出文件路径")
    parser.add_argument("--output-format", choices=OUTPUT_FORMATS, 
                       default=DEFAULT_OUTPUT_FORMAT, help="输出格式")
    
    # 通用参数
    parser.add_argument("--distributor", choices=list(SUPPORTED_DISTRIBUTORS.keys()),
                       default="digikey", help="分销商")
    parser.add_argument("--part-column", default="part_number", 
                       help="BOM文件中零件编号列名")
    parser.add_argument("--qty-column", default="quantity",
                       help="BOM文件中数量列名")
    parser.add_argument("--ref-column", default="reference",
                       help="BOM文件中参考标识符列名")
    parser.add_argument("--log-level", choices=["DEBUG", "INFO", "WARNING", "ERROR"],
                       default="INFO", help="日志级别")
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logger()
    logger.remove()  # 移除默认处理器
    logger.add(lambda msg: print(msg, end=""), level=args.log_level)
    
    try:
        if args.mode == "search":
            if not args.part_number:
                print("错误: 搜索模式需要指定 --part-number")
                return
            
            asyncio.run(search_single_part(args.part_number, args.distributor))
            
        elif args.mode == "analyze":
            if not args.bom_file:
                print("错误: 分析模式需要指定 --bom-file")
                return
            
            if not args.bom_file.exists():
                print(f"错误: BOM文件不存在: {args.bom_file}")
                return
            
            # 如果没有指定输出文件，自动生成
            if not args.output:
                output_name = f"{args.bom_file.stem}_analysis.{args.output_format}"
                args.output = args.bom_file.parent / output_name
            
            asyncio.run(analyze_bom_file(
                args.bom_file, args.output, args.output_format, args.distributor,
                args.part_column, args.qty_column, args.ref_column
            ))
    
    except KeyboardInterrupt:
        print("\n用户中断操作")
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        raise


if __name__ == "__main__":
    main()
