"""
数据模型定义
"""
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum


class ProductStatus(str, Enum):
    """产品状态枚举"""
    ACTIVE = "在售"
    OBSOLETE = "停产"
    NOT_RECOMMENDED = "不适用于新设计"
    LAST_TIME_BUY = "最后售卖"
    DISCONTINUED = "Digi-Key 停止提供"


class PackageType(str, Enum):
    """包装类型枚举"""
    BULK = "散装"
    TRAY = "托盘"
    TUBE = "管件"
    REEL = "卷带（TR）"
    CUT_TAPE = "剪切带（CT）"
    DIGI_REEL = "Digi-Reel® 得捷定制卷带"
    BAG = "袋"
    BOX = "盒"
    STRIP = "带"
    RETAIL = "零售封装"


class PriceBreak(BaseModel):
    """价格阶梯"""
    quantity: int = Field(description="数量")
    unit_price: float = Field(description="单价")
    currency: str = Field(default="USD", description="货币")


class ProductInfo(BaseModel):
    """产品信息模型"""
    # 基本信息
    part_number: str = Field(description="制造商零件编号")
    manufacturer: str = Field(description="制造商")
    description: str = Field(description="产品描述")
    datasheet_url: Optional[str] = Field(default=None, description="规格书链接")
    
    # 库存和价格
    stock_quantity: int = Field(default=0, description="库存数量")
    price_breaks: List[PriceBreak] = Field(default_factory=list, description="价格阶梯")
    minimum_quantity: int = Field(default=1, description="最小订购量")
    
    # 产品属性
    series: Optional[str] = Field(default=None, description="系列")
    package_type: Optional[str] = Field(default=None, description="包装类型")
    product_status: Optional[str] = Field(default=None, description="产品状态")
    
    # 技术参数（根据产品类型可能不同）
    specifications: Dict[str, Any] = Field(default_factory=dict, description="技术规格")
    
    # 元数据
    distributor: str = Field(description="分销商")
    distributor_part_number: Optional[str] = Field(default=None, description="分销商零件编号")
    product_url: Optional[str] = Field(default=None, description="产品页面链接")
    image_url: Optional[str] = Field(default=None, description="产品图片链接")
    
    # 时间戳
    crawled_at: datetime = Field(default_factory=datetime.now, description="爬取时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")


class BOMItem(BaseModel):
    """BOM条目"""
    reference: str = Field(description="参考标识符，如R1, C2等")
    part_number: str = Field(description="零件编号")
    description: Optional[str] = Field(default=None, description="描述")
    quantity: int = Field(default=1, description="数量")
    manufacturer: Optional[str] = Field(default=None, description="制造商")
    
    # 搜索结果
    search_results: List[ProductInfo] = Field(default_factory=list, description="搜索到的产品")
    selected_product: Optional[ProductInfo] = Field(default=None, description="选中的产品")
    
    # 状态
    search_status: str = Field(default="pending", description="搜索状态: pending, found, not_found, error")
    error_message: Optional[str] = Field(default=None, description="错误信息")


class BOMAnalysisResult(BaseModel):
    """BOM分析结果"""
    total_items: int = Field(description="总条目数")
    found_items: int = Field(description="找到的条目数")
    not_found_items: int = Field(description="未找到的条目数")
    error_items: int = Field(description="错误的条目数")
    
    total_cost: float = Field(default=0.0, description="总成本")
    currency: str = Field(default="USD", description="货币")
    
    items: List[BOMItem] = Field(description="BOM条目列表")
    
    # 元数据
    analysis_date: datetime = Field(default_factory=datetime.now, description="分析日期")
    distributor: str = Field(description="分销商")


class CrawlerResult(BaseModel):
    """爬虫结果"""
    success: bool = Field(description="是否成功")
    data: Optional[Any] = Field(default=None, description="数据")
    error_message: Optional[str] = Field(default=None, description="错误信息")
    execution_time: float = Field(default=0.0, description="执行时间（秒）")
    timestamp: datetime = Field(default_factory=datetime.now, description="时间戳")
