"""
安装脚本
"""
import subprocess
import sys
import os
from pathlib import Path


def run_command(command, description):
    """运行命令并显示结果"""
    print(f"\n{description}...")
    print(f"执行命令: {command}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True)
        print(f"✅ {description}成功")
        if result.stdout:
            print(f"输出: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description}失败")
        print(f"错误: {e.stderr}")
        return False


def check_python_version():
    """检查Python版本"""
    print("检查Python版本...")
    version = sys.version_info
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print(f"❌ Python版本过低: {version.major}.{version.minor}")
        print("需要Python 3.8或更高版本")
        return False
    
    print(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    return True


def install_requirements():
    """安装依赖包"""
    requirements_file = Path("requirements.txt")
    
    if not requirements_file.exists():
        print("❌ requirements.txt文件不存在")
        return False
    
    return run_command(
        f"{sys.executable} -m pip install -r requirements.txt",
        "安装Python依赖包"
    )


def install_playwright():
    """安装Playwright浏览器"""
    commands = [
        f"{sys.executable} -m pip install playwright",
        f"{sys.executable} -m playwright install chromium"
    ]
    
    for command in commands:
        if command.endswith("chromium"):
            description = "安装Playwright浏览器"
        else:
            description = "安装Playwright包"
        
        if not run_command(command, description):
            return False
    
    return True


def create_directories():
    """创建必要的目录"""
    print("\n创建项目目录...")
    
    directories = [
        "data",
        "data/downloads", 
        "data/processed",
        "logs"
    ]
    
    for dir_name in directories:
        dir_path = Path(dir_name)
        dir_path.mkdir(exist_ok=True)
        print(f"✅ 创建目录: {dir_path}")
    
    return True


def test_installation():
    """测试安装"""
    print("\n测试安装...")
    
    # 测试导入主要模块
    test_imports = [
        "playwright",
        "pandas",
        "loguru", 
        "pydantic",
        "aiohttp"
    ]
    
    for module in test_imports:
        try:
            __import__(module)
            print(f"✅ {module} 导入成功")
        except ImportError as e:
            print(f"❌ {module} 导入失败: {e}")
            return False
    
    return True


def main():
    """主安装函数"""
    print("BOM爬虫项目安装脚本")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 创建目录
    if not create_directories():
        print("❌ 创建目录失败")
        sys.exit(1)
    
    # 安装依赖包
    if not install_requirements():
        print("❌ 安装依赖包失败")
        sys.exit(1)
    
    # 安装Playwright
    if not install_playwright():
        print("❌ 安装Playwright失败")
        sys.exit(1)
    
    # 测试安装
    if not test_installation():
        print("❌ 安装测试失败")
        sys.exit(1)
    
    print("\n" + "=" * 50)
    print("✅ 安装完成！")
    print("\n下一步:")
    print("1. 运行测试: python test_crawler.py")
    print("2. 搜索零件: python main.py --mode search --part-number 'ATMEGA328P-AU'")
    print("3. 分析BOM: python main.py --mode analyze --bom-file example_bom.csv")


if __name__ == "__main__":
    main()
