"""
爬虫测试脚本
"""
import asyncio
import sys
from pathlib import Path
from loguru import logger

from digikey_crawler import DigiKeyCrawler
from bom_analyzer import BOMAnalyzer
from utils import setup_logger


async def test_digikey_search():
    """测试DigiKey搜索功能"""
    print("=== 测试DigiKey搜索功能 ===")
    
    test_parts = [
        "ATMEGA328P-AU",
        "RC0603FR-0710KL", 
        "CL10B104KB8NNNC",
        "LM358DR"
    ]
    
    crawler = DigiKeyCrawler()
    
    async with crawler:
        for part_number in test_parts:
            print(f"\n搜索零件: {part_number}")
            
            try:
                result = await crawler.search_product(part_number)
                
                if result.success and result.data:
                    print(f"✅ 找到 {len(result.data)} 个产品")
                    
                    # 显示第一个产品的详细信息
                    product = result.data[0]
                    print(f"   零件编号: {product.part_number}")
                    print(f"   制造商: {product.manufacturer}")
                    print(f"   描述: {product.description[:50]}...")
                    print(f"   库存: {product.stock_quantity}")
                    
                    if product.price_breaks:
                        price = product.price_breaks[0]
                        print(f"   价格: ${price.unit_price} ({price.quantity}+ 件)")
                    
                    print(f"   状态: {product.product_status}")
                    
                elif result.success:
                    print("❌ 未找到匹配产品")
                else:
                    print(f"❌ 搜索失败: {result.error_message}")
                    
            except Exception as e:
                print(f"❌ 测试失败: {e}")
            
            # 添加延迟避免请求过于频繁
            await asyncio.sleep(2)


async def test_bom_analysis():
    """测试BOM分析功能"""
    print("\n=== 测试BOM分析功能 ===")
    
    bom_file = Path("example_bom.csv")
    
    if not bom_file.exists():
        print("❌ 示例BOM文件不存在，跳过BOM分析测试")
        return
    
    try:
        analyzer = BOMAnalyzer("digikey")
        
        print(f"分析BOM文件: {bom_file}")
        result = await analyzer.analyze_bom_file(bom_file)
        
        print(f"\n分析结果:")
        print(f"  总条目数: {result.total_items}")
        print(f"  找到产品: {result.found_items}")
        print(f"  未找到: {result.not_found_items}")
        print(f"  错误: {result.error_items}")
        print(f"  成功率: {result.found_items/result.total_items*100:.1f}%")
        print(f"  预估总成本: ${result.total_cost:.2f}")
        
        # 显示前3个项目的详细结果
        print(f"\n前3个项目的详细结果:")
        for i, item in enumerate(result.items[:3], 1):
            print(f"\n  项目 {i}: {item.reference} - {item.part_number}")
            print(f"    状态: {item.search_status}")
            
            if item.selected_product:
                product = item.selected_product
                print(f"    匹配产品: {product.part_number}")
                print(f"    制造商: {product.manufacturer}")
                print(f"    库存: {product.stock_quantity}")
                
                if product.price_breaks:
                    price = product.price_breaks[0]
                    total_price = price.unit_price * item.quantity
                    print(f"    单价: ${price.unit_price}")
                    print(f"    总价: ${total_price:.2f} ({item.quantity} 件)")
            
            elif item.error_message:
                print(f"    错误: {item.error_message}")
        
        print("✅ BOM分析测试完成")
        
    except Exception as e:
        print(f"❌ BOM分析测试失败: {e}")


async def test_browser_setup():
    """测试浏览器设置"""
    print("=== 测试浏览器设置 ===")
    
    try:
        crawler = DigiKeyCrawler()
        
        print("启动浏览器...")
        await crawler.start_browser()
        
        print("导航到DigiKey首页...")
        success = await crawler.navigate_to_url("https://www.digikey.cn")
        
        if success:
            print("✅ 浏览器设置正常")
        else:
            print("❌ 无法访问DigiKey网站")
        
        await crawler.close_browser()
        
    except Exception as e:
        print(f"❌ 浏览器设置测试失败: {e}")


def check_dependencies():
    """检查依赖包"""
    print("=== 检查依赖包 ===")
    
    required_packages = [
        "playwright",
        "pandas", 
        "requests",
        "beautifulsoup4",
        "lxml",
        "openpyxl",
        "python-dotenv",
        "loguru",
        "pydantic",
        "aiohttp",
        "fake-useragent"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n缺少以下依赖包:")
        for package in missing_packages:
            print(f"  - {package}")
        print(f"\n请运行: pip install {' '.join(missing_packages)}")
        return False
    
    print("✅ 所有依赖包已安装")
    return True


async def main():
    """主测试函数"""
    print("BOM爬虫测试脚本")
    print("=" * 50)
    
    # 设置日志
    setup_logger()
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖检查失败，请先安装缺少的包")
        return
    
    try:
        # 测试浏览器设置
        await test_browser_setup()
        
        # 测试搜索功能
        await test_digikey_search()
        
        # 测试BOM分析功能
        await test_bom_analysis()
        
        print("\n" + "=" * 50)
        print("✅ 所有测试完成")
        
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        print(f"\n❌ 测试失败: {e}")


if __name__ == "__main__":
    asyncio.run(main())
