"""
工具函数
"""
import re
import time
import random
import asyncio
from typing import List, Dict, Any, Optional, Union
from pathlib import Path
import pandas as pd
from loguru import logger
from fake_useragent import UserAgent


def setup_logger(log_file: Optional[Path] = None) -> None:
    """设置日志"""
    from config import LOGS_DIR, LOG_CONFIG
    
    if log_file is None:
        log_file = LOGS_DIR / f"crawler_{time.strftime('%Y%m%d')}.log"
    
    logger.add(
        log_file,
        level=LOG_CONFIG["level"],
        format=LOG_CONFIG["format"],
        rotation=LOG_CONFIG["rotation"],
        retention=LOG_CONFIG["retention"],
        encoding="utf-8"
    )


def get_random_user_agent() -> str:
    """获取随机User-Agent"""
    try:
        ua = UserAgent()
        return ua.random
    except Exception:
        # 备用User-Agent
        return "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"


def random_delay(min_seconds: float = 1.0, max_seconds: float = 3.0) -> None:
    """随机延迟"""
    delay = random.uniform(min_seconds, max_seconds)
    time.sleep(delay)


async def async_random_delay(min_seconds: float = 1.0, max_seconds: float = 3.0) -> None:
    """异步随机延迟"""
    delay = random.uniform(min_seconds, max_seconds)
    await asyncio.sleep(delay)


def clean_text(text: str) -> str:
    """清理文本"""
    if not text:
        return ""
    
    # 移除多余的空白字符
    text = re.sub(r'\s+', ' ', text.strip())
    
    # 移除特殊字符
    text = re.sub(r'[^\w\s\-\.\,\(\)\[\]\/\+\=\:\;]', '', text)
    
    return text


def parse_price(price_str: str) -> float:
    """解析价格字符串"""
    if not price_str:
        return 0.0
    
    # 移除货币符号和逗号
    price_str = re.sub(r'[^\d\.]', '', price_str)
    
    try:
        return float(price_str)
    except ValueError:
        return 0.0


def parse_quantity(qty_str: str) -> int:
    """解析数量字符串"""
    if not qty_str:
        return 0
    
    # 移除非数字字符
    qty_str = re.sub(r'[^\d]', '', qty_str)
    
    try:
        return int(qty_str)
    except ValueError:
        return 0


def normalize_part_number(part_number: str) -> str:
    """标准化零件编号"""
    if not part_number:
        return ""
    
    # 转换为大写并移除多余空格
    part_number = part_number.upper().strip()
    
    # 移除常见的前缀/后缀
    prefixes_to_remove = ["MPN:", "P/N:", "PN:", "PART#:", "PART NUMBER:"]
    for prefix in prefixes_to_remove:
        if part_number.startswith(prefix):
            part_number = part_number[len(prefix):].strip()
    
    return part_number


def load_bom_file(file_path: Union[str, Path]) -> List[Dict[str, Any]]:
    """加载BOM文件"""
    file_path = Path(file_path)
    
    if not file_path.exists():
        raise FileNotFoundError(f"BOM文件不存在: {file_path}")
    
    file_ext = file_path.suffix.lower()
    
    try:
        if file_ext == '.csv':
            df = pd.read_csv(file_path, encoding='utf-8')
        elif file_ext in ['.xlsx', '.xls']:
            df = pd.read_excel(file_path)
        else:
            raise ValueError(f"不支持的文件格式: {file_ext}")
        
        # 转换为字典列表
        return df.to_dict('records')
    
    except Exception as e:
        logger.error(f"加载BOM文件失败: {e}")
        raise


def save_results(data: List[Dict[str, Any]], output_path: Union[str, Path], 
                format: str = "csv") -> None:
    """保存结果"""
    output_path = Path(output_path)
    
    if not data:
        logger.warning("没有数据需要保存")
        return
    
    try:
        df = pd.DataFrame(data)
        
        if format.lower() == "csv":
            df.to_csv(output_path, index=False, encoding='utf-8-sig')
        elif format.lower() in ["xlsx", "excel"]:
            df.to_excel(output_path, index=False)
        elif format.lower() == "json":
            df.to_json(output_path, orient='records', indent=2, ensure_ascii=False)
        else:
            raise ValueError(f"不支持的输出格式: {format}")
        
        logger.info(f"结果已保存到: {output_path}")
    
    except Exception as e:
        logger.error(f"保存结果失败: {e}")
        raise


def validate_part_number(part_number: str) -> bool:
    """验证零件编号格式"""
    if not part_number or len(part_number.strip()) < 2:
        return False
    
    # 基本格式检查：包含字母或数字
    if not re.search(r'[A-Za-z0-9]', part_number):
        return False
    
    return True


def extract_specifications(spec_text: str) -> Dict[str, str]:
    """从规格文本中提取规格信息"""
    specs = {}
    
    if not spec_text:
        return specs
    
    # 常见的规格模式
    patterns = [
        r'(\w+):\s*([^,\n]+)',  # Key: Value
        r'(\w+)\s*=\s*([^,\n]+)',  # Key = Value
        r'(\w+)\s+([0-9]+[A-Za-z]*)',  # Key Value (数字+单位)
    ]
    
    for pattern in patterns:
        matches = re.findall(pattern, spec_text, re.IGNORECASE)
        for key, value in matches:
            specs[key.strip()] = value.strip()
    
    return specs
